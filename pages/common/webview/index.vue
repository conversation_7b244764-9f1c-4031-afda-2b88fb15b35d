<template>
  <view v-if="finalUrl">
    <web-view :webview-styles="webviewStyles" :src="finalUrl"></web-view>
  </view>
</template>

<script>
  import { getAccessToken } from '@/utils/auth'
  
  export default {
    data() {
      return {
        params: {},
        finalUrl: '',
        webviewStyles: {
          progress: {
            color: "#FF3333"
          }
        }
      }
    },
    props: {
      src: {
        type: [String],
        default: null
      }
    },
    onLoad(event) {
      this.params = event
      if (event.title) {
        uni.setNavigationBarTitle({
          title: event.title
        })
      }
      
      // 检查登录状态
      this.checkLogin();
      
      // 处理URL，添加token
      if (event.url) {
        const token = getAccessToken();
        if (token) {
          // 判断URL是否已经有参数
          const separator = event.url.includes('?') ? '&' : '?';
          this.finalUrl = `${event.url}${separator}access_token=${token}`;
        } else {
          this.finalUrl = event.url;
        }
      }
    },
    methods: {
      // 检查登录状态
      checkLogin() {
        if (!getAccessToken()) {
          this.$modal.showToast('请先登录');
          setTimeout(() => {
            this.$tab.reLaunch('/pages/login');
          }, 1500);
        }
      }
    }
  }
</script>
